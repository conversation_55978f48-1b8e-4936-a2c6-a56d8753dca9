// Core types for ReedSoft application

export interface NavLink {
  href: string;
  label: string;
  external?: boolean;
}

export interface Service {
  id: string;
  title: string;
  slug: string;
  summary: string;
  priceFrom?: number;
  features: string[];
  sections?: ServiceSection[];
  faqs?: FAQ[];
}

export interface ServiceSection {
  title: string;
  content: string;
}

export interface FAQ {
  question: string;
  answer: string;
}

export interface Package {
  id: string;
  name: string;
  priceFrom: number;
  features: string[];
  ctaLabel: string;
  popular?: boolean;
}

export interface PortfolioItem {
  id: string;
  title: string;
  slug: string;
  industry: string;
  isStudent: boolean;
  problem: string;
  solution: string;
  tech: string[];
  images: string[];
  liveUrl?: string;
  result?: string;
  testimonialRef?: string;
}

export interface Testimonial {
  id: string;
  author: string;
  role: string;
  company: string;
  quote: string;
  avatar?: string;
}

export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  body: string;
  coverImage?: string;
  tags: string[];
  publishedAt: string;
  seo?: SEOData;
}

export interface SEOData {
  title?: string;
  description?: string;
  keywords?: string[];
  ogImage?: string;
}

export interface ContactForm {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  message: string;
  serviceType?: string;
  budget?: string;
}

export interface QuoteForm extends ContactForm {
  projectType: string;
  timeline: string;
  requirements: string;
}
