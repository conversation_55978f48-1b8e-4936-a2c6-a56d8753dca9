import { <PERSON><PERSON>, <PERSON>, FeatureGrid, Footer } from '@/components';
import { NavLink } from '@/types';

const navLinks: NavLink[] = [
  { href: '/', label: 'Home' },
  { href: '/services', label: 'Services' },
  { href: '/student-plans', label: 'Student Plans' },
  { href: '/portfolio', label: 'Portfolio' },
  { href: '/pricing', label: 'Pricing' },
  { href: '/blog', label: 'Blog' },
  { href: '/contact', label: 'Contact' },
];

const features = [
  {
    icon: (
      <svg
        className="h-8 w-8"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
        />
      </svg>
    ),
    title: 'Web Development',
    shortDesc:
      'Modern, responsive websites built with the latest technologies for optimal performance and user experience.',
  },
  {
    icon: (
      <svg
        className="h-8 w-8"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"
        />
      </svg>
    ),
    title: 'Mobile Apps',
    shortDesc:
      'Native and cross-platform mobile applications that deliver seamless experiences across all devices.',
  },
  {
    icon: (
      <svg
        className="h-8 w-8"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
        />
      </svg>
    ),
    title: 'Student Services',
    shortDesc:
      'Specialized academic support including thesis development, research assistance, and educational technology solutions.',
  },
];

export default function Home() {
  return (
    <div className="min-h-screen">
      <Header navLinks={navLinks} />

      <Hero
        headline="Professional Web Development & Student Services"
        subheadline="Fast, reliable, and affordable solutions for businesses and students in the Philippines. From modern websites to academic support, we deliver excellence."
        primaryCTA={{
          text: 'Get Started',
          href: '/quote',
        }}
        secondaryCTA={{
          text: 'View Portfolio',
          href: '/portfolio',
        }}
      />

      <FeatureGrid
        title="Our Services"
        subtitle="Comprehensive digital solutions tailored to your needs"
        items={features}
      />

      <Footer
        navLinks={navLinks}
        contactInfo={{
          email: '<EMAIL>',
          phone: '+63 XXX XXX XXXX',
          address: 'Philippines',
        }}
      />
    </div>
  );
}
