'use client';

import Link from 'next/link';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { NavLink } from '@/types';

interface HeaderProps {
  logo?: string;
  navLinks: NavLink[];
  primaryCTAText?: string;
  sticky?: boolean;
}

export function Header({
  logo = 'ReedSoft',
  navLinks,
  primaryCTAText = 'Request a Quote',
  sticky = true,
}: HeaderProps) {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header
      className={`z-50 w-full border-b border-gray-200 bg-white ${
        sticky ? 'sticky top-0' : ''
      }`}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link href="/" className="text-brand-primary text-2xl font-bold">
              {logo}
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden items-center space-x-8 md:flex">
            {navLinks.map(link => (
              <Link
                key={link.href}
                href={link.href}
                className="hover:text-brand-primary text-gray-700 transition-colors duration-200"
                {...(link.external && {
                  target: '_blank',
                  rel: 'noopener noreferrer',
                })}
              >
                {link.label}
              </Link>
            ))}
          </nav>

          {/* CTA Button */}
          <div className="hidden md:flex">
            <Button
              asChild
              className="bg-brand-primary hover:bg-brand-primary/90"
            >
              <Link href="/quote">{primaryCTAText}</Link>
            </Button>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="hover:text-brand-primary focus:text-brand-primary text-gray-700 focus:outline-none"
              aria-label="Toggle menu"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                {isMenuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden">
            <div className="space-y-1 border-t border-gray-200 bg-white px-2 pt-2 pb-3 sm:px-3">
              {navLinks.map(link => (
                <Link
                  key={link.href}
                  href={link.href}
                  className="hover:text-brand-primary block px-3 py-2 text-gray-700 transition-colors duration-200"
                  onClick={() => setIsMenuOpen(false)}
                  {...(link.external && {
                    target: '_blank',
                    rel: 'noopener noreferrer',
                  })}
                >
                  {link.label}
                </Link>
              ))}
              <div className="px-3 py-2">
                <Button
                  asChild
                  className="bg-brand-primary hover:bg-brand-primary/90 w-full"
                >
                  <Link href="/quote">{primaryCTAText}</Link>
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
