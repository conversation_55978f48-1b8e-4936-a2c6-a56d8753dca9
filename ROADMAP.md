Reedsoft Web Creations — Implementation Roadmap

Overview
This roadmap outlines phases, milestones, timeline estimates, dependencies, and priorities to deliver a modern, high-performing portfolio/lead-generation site for a freelance web development agency. The plan assumes a small team (1–2 devs) and parallelizable content work.

Legend

- Priority: M (Must), S (Should), C (Could), W (Won’t for v1)
- Durations are estimates assuming ~25–30 focused hours/week
- Deliverables have lightweight acceptance criteria

Phase 0 — Discovery & Setup (Week 1) [M]
Milestones

- Confirm objectives, scope, personas, and KPIs (see PLAN.md section 12–13)
- Finalize information architecture and top nav
- Branding choices: palette, typography, components baseline
- Repo, CI, environments created (Local/Preview/Prod on Vercel)
- Tech decisions confirmed: Next.js, Tailwind + shadcn/ui, Zustand, Sanity, Supabase, Resend, GA4, Upstash
  Deliverables
- Signed-off sitemap and wireframes
- Vercel project with preview deployments from PRs
- Base Next.js app with TypeScript, ESLint/Prettier, Tailwind, shadcn/ui installed
  Dependencies: none
  Risks: scope uncertainty (mitigate with MoSCoW and change log)

Phase 1 — Design System & Foundation (Week 2) [M]
Milestones

- Design tokens (colors, spacing, typography), light/dark mode
- Core components scaffolded (Header, Footer, Hero, Buttons, Forms, Cards)
- Accessibility baseline (focus rings, skip links, keyboard nav)
- Analytics + cookie banner skeleton
  Deliverables
- Component library documented (Storybook optional)
- Lint, type-check, unit test pipeline green
  Dependencies: Phase 0
  Risks: design churn (timebox decisions)

Phase 2 — Static MVP Pages (Week 3) [M]
Milestones

- Home, About, Services (index), Student Plans, Pricing, Contact pages with static content
- Responsive layouts with shadcn/ui patterns
- SEO foundations: head tags, sitemap/robots, OpenGraph defaults
  Deliverables
- Mobile Lighthouse ≥90, CLS <0.1 on key pages
  Dependencies: Phase 1
  Risks: content availability (use placeholders)

Phase 3 — CMS Integration (Week 4) [M]
Milestones

- Sanity schemas for services, packages, portfolio, testimonials, blog, site settings
- CMS-driven Services and Pricing pages
- Draft preview mode and on-demand ISR via webhooks
  Deliverables
- Non-technical edits visible in Preview within seconds
  Dependencies: Phase 2
  Risks: content model changes (keep schemas extensible)

Phase 4 — Inquiry & Student Quote Flows (Week 5) [M]
Milestones

- Contact and Quick Quote forms using React Hook Form + Zod
- Server Route Handlers: validation, Supabase persistence, Resend emails
- Anti-spam (hCaptcha/reCAPTCHA v3), rate limiting with Upstash
- Thank-you page with booking link (e.g., Calendly embed)
  Deliverables
- End-to-end tested form submissions; dedupe within 10 minutes
  Dependencies: Phase 2 (UI), Phase 0 (env), partial Phase 3 (site settings for emails)
  Risks: email deliverability (configure DKIM/SPF early)

Phase 5 — Portfolio & Blog (Week 6) [S]
Milestones

- Portfolio grid + detail pages powered by Sanity; OG image generation per case study
- Optional Blog (MDX or Sanity), RSS feed, categories/tags
  Deliverables
- At least 3 case studies published; optional 2 blog posts
  Dependencies: Phase 3
  Risks: content production time (parallelize writing)

Phase 6 — SEO, Performance, Accessibility Hardening (Week 7) [M]
Milestones

- Structured data (Organization, Service), next-seo enhancements
- Image optimization, route-level caching, critical CSS tuning
- Accessibility pass (axe/Playwright), keyboard-only QA
  Deliverables
- Lighthouse Mobile ≥90 across Performance/SEO/Best Practices/Accessibility
  Dependencies: Phases 2–5
  Risks: third-party scripts impacting scores (defer/consent-gate)

Phase 7 — Integrations (Payments/Booking) (Week 8) [C]
Milestones

- Payment links guidance or simple integration (PayMongo/Xendit) for invoices or deposits
- Booking embed (Calendly/Google Calendar) with tracking
  Deliverables
- Tested embeds with event tracking
  Dependencies: Phase 4 (thank-you flow)
  Risks: provider limitations; provide fallbacks

Phase 8 — Admin View (Read-only v1) (Week 9) [S]
Milestones

- Minimal admin area protected by Supabase Auth (magic link)
- List/search inquiries; export CSV
  Deliverables
- Secure read-only dashboard for triage
  Dependencies: Phase 4
  Risks: scope creep (keep minimal)

Phase 9 — Launch & Handover (Week 10) [M]
Milestones

- Final content pass, QA, domain setup, redirects, monitoring
- Playbook: how to edit content, publish, revalidate, manage env vars
- Post-launch plan: bugfix window, backlog grooming for v2
  Deliverables
- Production go-live with analytics verified
  Dependencies: All prior Musts complete
  Risks: DNS/propagation delays (plan buffer)

Timeline Summary

- Weeks 1–3: Foundation + Static MVP
- Weeks 4–5: CMS + Forms (lead gen live by end of Week 5)
- Weeks 6–7: Content expansion + SEO/perf hardening
- Weeks 8–10: Optional integrations/admin + Launch

Dependencies Map (high-level)

- Phase 1 depends on 0
- Phase 2 depends on 1
- Phase 3 depends on 2
- Phase 4 depends on 2 and partial 3
- Phase 5 depends on 3
- Phase 6 depends on 2–5
- Phase 7 depends on 4
- Phase 8 depends on 4
- Phase 9 depends on all Musts

Feature Priorities (MoSCoW)
Must: Home, About, Services, Student Plans, Pricing, Contact; Inquiry flows; CMS for Services/Pricing; SEO basics; Analytics; Performance baseline
Should: Portfolio (min 3), Admin read-only view, Cookie banner, OG images, Blog
Could: Payments/Booking embeds, Dark mode, Search, Newsletter
Won’t (v1): Full e-commerce, complex booking engines, role-based admin, i18n

Risk Register (selected)

- Content delays: mitigate with placeholders and parallel writing
- Email deliverability: set up DKIM/SPF/DMARC; test multiple inboxes
- Spam/abuse: captcha + rate limit; server-side validation
- Vendor lock-in: keep CMS schemas portable; export scripts

Success Metrics & QA Gates

- Conversion: ≥3% inquiry rate; tracked events for quote_submitted
- Performance: LCP ≤2.5s; CLS <0.1; TBT <200ms (lab)
- Accessibility: axe violations 0 critical/blocking
- SEO: Indexing complete, brand query ranks top 1–3 locally

Post-Launch Backlog (v2 candidates)

- Role-based admin with notes/status, email templates, CRM integration (HubSpot/Zoho)
- Advanced search and content personalization
- Additional case studies and lead magnets
- Localization and additional payment providers
