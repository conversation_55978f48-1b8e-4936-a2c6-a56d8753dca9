{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"NEXT_PUBLIC_SUPABASE_URL": "@supabase-url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase-anon-key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase-service-role-key", "NEXT_PUBLIC_SANITY_PROJECT_ID": "@sanity-project-id", "NEXT_PUBLIC_SANITY_DATASET": "@sanity-dataset", "SANITY_API_TOKEN": "@sanity-api-token", "SANITY_WEBHOOK_SECRET": "@sanity-webhook-secret", "RESEND_API_KEY": "@resend-api-key", "RESEND_FROM_EMAIL": "@resend-from-email", "NEXT_PUBLIC_GA_MEASUREMENT_ID": "@ga-measurement-id", "UPSTASH_REDIS_REST_URL": "@upstash-redis-url", "UPSTASH_REDIS_REST_TOKEN": "@upstash-redis-token", "NEXT_PUBLIC_HCAPTCHA_SITE_KEY": "@hcaptcha-site-key", "HCAPTCHA_SECRET_KEY": "@hcaptcha-secret-key", "NEXTAUTH_SECRET": "@nextauth-secret"}, "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}, {"source": "/robots.txt", "destination": "/api/robots"}]}