import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/button';

interface HeroProps {
  headline: string;
  subheadline: string;
  primaryCTA: {
    text: string;
    href: string;
  };
  secondaryCTA?: {
    text: string;
    href: string;
  };
  media?: {
    type: 'image' | 'video';
    src: string;
    alt?: string;
  };
}

export function Hero({
  headline,
  subheadline,
  primaryCTA,
  secondaryCTA,
  media,
}: HeroProps) {
  return (
    <section className="from-brand-neutral-light relative bg-gradient-to-br to-white py-20 lg:py-32">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
          {/* Content */}
          <div className="text-center lg:text-left">
            <h1 className="text-brand-neutral-dark mb-6 text-4xl leading-tight font-bold sm:text-5xl lg:text-6xl">
              {headline}
            </h1>
            <p className="mx-auto mb-8 max-w-2xl text-xl text-gray-600 lg:mx-0">
              {subheadline}
            </p>

            {/* CTAs */}
            <div className="flex flex-col justify-center gap-4 sm:flex-row lg:justify-start">
              <Button
                asChild
                size="lg"
                className="bg-brand-primary hover:bg-brand-primary/90 rounded-2xl px-8 py-4 text-lg text-white"
              >
                <Link href={primaryCTA.href}>{primaryCTA.text}</Link>
              </Button>

              {secondaryCTA && (
                <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="border-brand-primary text-brand-primary hover:bg-brand-primary rounded-2xl px-8 py-4 text-lg hover:text-white"
                >
                  <Link href={secondaryCTA.href}>{secondaryCTA.text}</Link>
                </Button>
              )}
            </div>
          </div>

          {/* Media */}
          {media && (
            <div className="relative">
              {media.type === 'image' ? (
                <Image
                  src={media.src}
                  alt={media.alt || 'Hero image'}
                  width={600}
                  height={400}
                  className="h-auto w-full rounded-lg shadow-2xl"
                />
              ) : (
                <video
                  src={media.src}
                  autoPlay
                  muted
                  loop
                  className="h-auto w-full rounded-lg shadow-2xl"
                />
              )}
            </div>
          )}
        </div>
      </div>

      {/* Background decoration */}
      <div className="absolute top-0 right-0 -z-10 opacity-10">
        <svg width="404" height="404" fill="none" viewBox="0 0 404 404">
          <defs>
            <pattern
              id="85737c0e-0916-41d7-917f-596dc7edfa27"
              x="0"
              y="0"
              width="20"
              height="20"
              patternUnits="userSpaceOnUse"
            >
              <rect
                x="0"
                y="0"
                width="4"
                height="4"
                className="text-brand-primary"
                fill="currentColor"
              />
            </pattern>
          </defs>
          <rect
            width="404"
            height="404"
            fill="url(#85737c0e-0916-41d7-917f-596dc7edfa27)"
          />
        </svg>
      </div>
    </section>
  );
}
