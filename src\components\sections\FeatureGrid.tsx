interface FeatureItem {
  icon: React.ReactNode;
  title: string;
  shortDesc: string;
}

interface FeatureGridProps {
  items: FeatureItem[];
  title?: string;
  subtitle?: string;
}

export function FeatureGrid({ items, title, subtitle }: FeatureGridProps) {
  return (
    <section className="bg-white py-16">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        {(title || subtitle) && (
          <div className="mb-12 text-center">
            {title && (
              <h2 className="text-brand-neutral-dark mb-4 text-3xl font-bold sm:text-4xl">
                {title}
              </h2>
            )}
            {subtitle && (
              <p className="mx-auto max-w-3xl text-xl text-gray-600">
                {subtitle}
              </p>
            )}
          </div>
        )}

        {/* Grid */}
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {items.map((item, index) => (
            <div
              key={index}
              className="rounded-lg p-6 text-center transition-shadow duration-300 hover:shadow-lg"
            >
              {/* Icon */}
              <div className="mb-4 flex justify-center">
                <div className="bg-brand-primary/10 text-brand-primary flex h-16 w-16 items-center justify-center rounded-full">
                  {item.icon}
                </div>
              </div>

              {/* Content */}
              <h3 className="text-brand-neutral-dark mb-3 text-xl font-semibold">
                {item.title}
              </h3>
              <p className="leading-relaxed text-gray-600">{item.shortDesc}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
