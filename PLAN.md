Reedsoft Web Creations — Website Spec
1 — High-level goal
A clean, fast, mobile-first business site that converts two audiences:

IT students (capstone/thesis clients): quick-turn, affordable, delivered with documentation.

Small & medium Filipino businesses: e-commerce, booking, inventory systems, landing pages.

Primary conversion actions: Request a Quote (general), Student Quote (student-specific), and View Packages.

2 — Visual style & branding
Overall feel: Modern, professional, approachable. Emphasize reliability and speed.

Color palette: Pull from your logo; if unavailable, use:

Primary: deep teal (#0B6B60) or navy (#0F4C75)

Accent: warm gold (#F2C94C) or coral (#FF7A66)

Neutral: off-white (#FAFBFC), warm gray (#F3F4F6), dark gray (#333A44)

Typography:

Headings: Inter / Poppins — bold, condensed for H1.

Body: Roboto / Open Sans — regular 16px base.

Style: Minimalist grid, rounded 8px card corners, subtle drop shadows, 2xl rounded CTA buttons.

Imagery: Abstract tech illustrations, devices mockups, and local Filipino small-business scenes. No people if that conflicts with brand images.

3 — Homepage layout (top to bottom)
Header (sticky)

Left: logo. Right: nav links (Home, Services, Student Plans, Portfolio, Pricing, Blog, Contact) + CTA button Request a Quote. Mobile: hamburger menu.

Hero (primary conversion)

Left (or top on mobile): Headline, Subheadline, Primary CTAs.

Right (or below on mobile): device mockup or illustration.

Hero copy (paste-ready):

Headline: Websites & Systems That Work — From Capstones to Commerce.

Subheadline: Fast, reliable development and deployment — affordable student plans, scalable business solutions.

CTAs: [Request a Free Quote] (primary) — [Student Quote] (secondary)

Trust / Why Us (3–4 column features)

Quick bullets: Student-friendly docs, Fast delivery, Filipino payment & deployment, Post-launch support.

Services Overview (cards)

Website (3–5 pages), E-commerce (up to 20 products), Booking System, Inventory System, Thesis/Capstone Package.

Each card: short benefit line + small icon + Learn More.

Student Spotlight (small section)

Short pitch, what's included for students (documentation, GitHub repo, deployment, grading support), Request Student Quote.

Packages & Pricing (teaser)

Show 3 packages (Basic / Business / Custom) with starting prices and top-line scope. CTA: View Full Pricing.

Portfolio / Case Studies (carousel/grid)

3 featured items with thumbnails, short problem/solution/result, tech tags, View Case Study.

Testimonials (rotating quotes)

Student testimonial + small business testimonial.

Lead magnet / Promo

Offer: “Launch discount for first 3 clients” or “Free 1-month maintenance for first 2 business clients.”

Contact / Inquiry Form (see fields below) + CTA to schedule Zoom

Footer

Links, social icons, copyright, small privacy & terms links, office/contact info.

4 — Pages & essential content
Home — as above.

About — short team story, mission, why Reedsoft, logo and trust marks.

Services (main) — each service has a subpage with scope, examples, timeline.

Student Plans — student-specific offers, deliverables, sample grade-ready repo.

Pricing — clear package comparison table + add-ons.

Portfolio — case studies (see template below).

Blog/Resources — short how-tos, thesis tips, SEO basics for biz.

Contact — form + booking link + Google Map (if you have office).

Privacy & Terms — GDPR-lite / PH privacy brief.

5 — Inquiry form (pre-qualification fields)
Put this on Contact + Quick Quote modal:

Full name

Email

Phone (optional)

Company / University (text)

Project type (radio): Student (capstone), Website, E-commerce, Booking, Inventory, Other

Desired launch date (date)

Estimated budget (radio): <₱10k, ₱10k–₱30k, ₱30k–₱70k, ₱70k+ / or custom text

Short project description (textarea)

Reference sites (URLs)

Files (optional — PDF, images)

How did you hear about us? (dropdown)

Checkbox: “I agree to the privacy policy” (link)

Add spam protection (reCAPTCHA or hCaptcha). Auto-response email: “Thanks — we’ll respond in X business days.”

6 — Service packages (short, for Pricing page)
Basic (Student / Landing) — Starting ₱6,000

3–5 pages, responsive, contact form, basic SEO, Git repo + deployment guide, 1 revision.

Business — Starting ₱25,000

Everything in Basic + e-commerce up to 20 products, blog, social integration, payment gateway advice, 2 revisions.

Custom — Starting ₱60,000

Custom design, API integrations, booking/inventory systems, admin dashboards, 3 revisions, SLA & maintenance plan.

Add-ons: Logo design, copywriting, extra revisions, advanced SEO, monthly maintenance, premium hosting.

(These price suggestions are placeholders — tweak to match your local market & expertise.)

7 — Portfolio case study template (what to display)
For each item show:

Project title & short one-liner

Industry / for student or business

Problem (2–3 sentences)

Our solution (features delivered)

Tech stack (e.g., .NET/React/Next/Gatsby/WordPress)

Screenshots & live link

Outcome / metrics (optional)

Testimonial (if available)

8 — Component list for the AI code generator
Create reusable components with props:

Header(logo, navLinks[], primaryCTAText, sticky=true)

Hero(headline, subheadline, primaryCTA, secondaryCTA, media)

FeatureGrid(items[]) — item: {icon, title, shortDesc}

ServiceCard(title, summary, priceFrom, link)

PackageTable(packages[]) — package: {name, priceFrom, bullets[], cta}

PortfolioGrid(items[], filterTags[])

TestimonialCarousel(items[])

ContactForm(fields[], onSubmit) (with validation + reCAPTCHA)

Footer(links, socials)

Modal(quoteForm) for quick quote

Behavior: animated microinteractions on hover, accessible keyboard navigation.

9 — Tech stack & CMS suggestions
Static + CMS (recommended quick deploy): Next.js (React) + Vercel for hosting + headless CMS like Strapi / Sanity or Netlify CMS. Fast, SEO-friendly, easy to iterate.

Alternative (WordPress): WordPress + Elementor or Astra for rapid builds and client edits (good for non-technical clients).

Minimal/Student builds: Simple static HTML/CSS + Netlify for free hosting (fast for capstones).

Database/API needs: For booking/inventory use PostgreSQL + Firebase / Supabase, or integrate with existing SaaS (Calendly, Bookly).

Payments (Philippines): PayMongo or Xendit integrations.

Analytics & tracking: Google Analytics 4 + Facebook Pixel (for Ads).

Email & forms: Form backend: Formspree / Netlify Forms / custom endpoint + sendgrid for emails.

10 — SEO, performance & accessibility checklist
Meta title and description per page.

Open Graph tags for social sharing.

Fast load: images optimized (WebP), lazy-load, CDN (Vercel/Netlify).

Lighthouse score target: 90+.

Keyboard accessible & semantic HTML, alt text for images.

Mobile-first breakpoints; ensure CTAs prominent above-the-fold on phones.

11 — Deployment & file structure (suggested)
src/components/ (all components)

src/pages/ (Home, About, Services...)

public/assets/ (images, logo)

content/ or headless CMS for posts & portfolio

Deploy on Vercel (Next.js) or Netlify (React/Vite). Use CI from GitHub.

12 — Project objectives & scope (enhanced)
Objectives (measurable)

- Generate qualified leads: ≥3–5% homepage-to-inquiry conversion rate within 60 days of launch
- Performance: Lighthouse ≥90 on Mobile for Performance/Best Practices/SEO; LCP ≤2.5s on 4G; TTI ≤3s
- SEO: Rank for brand name and 3 core service keywords locally within 60–90 days; publish 4 blog posts in first 60 days
- Operations: First response SLA ≤1 business day; automated acknowledgement within 5 minutes

In-scope

- Marketing site with pages defined in sections 3–4
- Quote and student-specific inquiry flows with email + persistence
- Portfolio and blog powered by a CMS
- Basic admin/dashboard to view inquiries (read-only v1)
- Analytics, event tracking, and cookie consent banner
- Deployment with preview environments and on-demand ISR revalidation

Out-of-scope (v1)

- Full e-commerce (cart/checkout) and complex booking engines (integrate/embed only)
- Multilingual i18n, role-based admin, granular CRM, payments collection
- Advanced search and content personalization (consider for v2)

13 — Personas & primary use cases
Personas

- IT student (Capstone/Thesis)
  Goal: Affordable, documented, on-time delivery; clear grading artifacts
- SMB owner/manager
  Goal: Understand offerings, pricing, credibility; request quote/consult quickly
- Reedsoft admin
  Goal: Receive/triage leads, respond fast, publish content without dev help

User stories (abbrev.)

- As a student, I can see what’s included in Student Plans and submit a Student Quote with required fields so I get a tailored estimate.
- As an SMB owner, I can scan packages, see relevant portfolio items, and request a quote in ≤2 minutes from mobile.
- As an admin, I can view new inquiries in a secure dashboard and get an email alert with key details.

High-level acceptance criteria

- All primary CTAs reachable and keyboard-accessible within first tab sequence
- Forms validate inline, prevent spam, send email + store record, and show success state
- Portfolio/blog content editable in CMS; publishing triggers revalidation

14 — Feature specifications & acceptance criteria (selected highlights)
Header & navigation

- Sticky header, logo, nav links, primary CTA; hamburger on mobile; active link highlighting
- Accessibility: proper roles/aria-expanded, focus traps for mobile menu
  Acceptance: Works across Chrome/Safari/Firefox/Edge, mobile/desktop; keyboard navigation complete

Hero

- Headline, subheadline, primary and secondary CTAs, responsive media (Next/Image)
  Acceptance: CLS < 0.1; CTAs visible above-the-fold on 360px width devices

Services & service detail pages

- Services index (cards) and service detail pages fetched from CMS; sections for scope, timeline, inclusions, FAQs
  Acceptance: Each service page has unique meta; breadcrumb; schema.org Service markup

Student Plans

- Clear deliverables (docs, repo, deployment), process steps, student-specific quote CTA
  Acceptance: Student quote form pre-fills project type and routes to appropriate pipeline label

Pricing

- PackageTable with Basic/Business/Custom; features pulled from CMS
  Acceptance: Toggle currency/notes; footnotes for assumptions

Portfolio

- Grid with filters/tags; case study detail pages with problem/solution/tech/outcome
  Acceptance: Lazy-load images; OpenGraph images per case study

Blog/Resources (optional v1 if time permits)

- Index with categories; MDX or CMS-backed posts; code/CTA components
  Acceptance: RSS feed, sitemap entries, canonical URLs

Contact & Quick Quote forms

- Fields per section 5; validation (Zod); anti-spam (hCaptcha or reCAPTCHA v3); rate limiting
- Server-side: store in DB; send transactional email and internal notification; thank-you page with calendar link
  Acceptance: Duplicate submission protection within 10 minutes; failure fallback email path

Footer

- Links, socials, address, privacy/terms; structured data for Organization
  Acceptance: All external links have rel attributes; email/phone clickable

15 — Technical architecture & stack (recommended)
Core

- Next.js 14 (App Router) + TypeScript on Vercel; file-based routing, Route Handlers for APIs, ISR/SSR as needed
- UI: Tailwind CSS + shadcn/ui (Radix UI primitives) for accessible, composable components; lucide-react for icons
- State: Zustand for lightweight client state (menus, modals, filters); server data via fetch/React cache
- Forms: React Hook Form + Zod resolver; shadcn/ui form primitives for consistent UX

Content and data

- CMS: Sanity.io for services, packages, portfolio, testimonials, blog, and site settings; preview drafts; webhooks to revalidate
- Database/Auth: Supabase (Postgres + Auth + Storage) for inquiry storage, admin login (magic link), and file uploads if needed
- Email: Resend for transactional emails (auto-ack + internal alert); set up domain DKIM
- Analytics: GA4 + Vercel Web Analytics; cookie consent banner (CookieYes or open-source)
- Search/SEO: next-seo for meta utilities; next-sitemap for sitemap/robots
- Rate limiting: Upstash Redis (Vercel integration) on quote endpoints

Testing/quality

- Unit: Vitest or Jest + Testing Library; E2E: Playwright
- Lint/format/type: ESLint (next/core-web-vitals), Prettier, TypeScript strict
- Monitoring: Sentry (optional)

Why this stack works well together

- Next.js + Vercel provide first-class DX, image/edge capabilities, and ISR for a fast marketing site
- shadcn/ui with Tailwind gives a consistent, accessible design system while staying close to vanilla React
- Zustand keeps client state minimal and explicit; server state remains server-driven via Next.js
- Sanity gives non-technical editing with drafts and live preview; Supabase complements it for structured lead data and auth without building a custom CMS
- Resend integrates cleanly with Next Route Handlers; GA4/Vercel cover analytics with low overhead

Potential integration challenges and solutions

- Sanity + ISR: Use Sanity webhooks to call /api/revalidate on publish; implement on-demand revalidation per route
- Supabase auth in App Router: Prefer server-side session checks in Route Handlers; use RLS policies and service role key only on server
- shadcn theming: Centralize design tokens, enable CSS variables for dark mode; avoid uncontrolled global styles
- Captcha with SSR: Load via client-only component; verify tokens in server Route Handler
- Email deliverability: Configure DKIM/SPF; use a transactional subdomain; implement retry/backoff
- Rate limiting on serverless: Use IP + email hash keys with sliding window in Upstash Redis

16 — Content model (Sanity schemas outline)

- service: {title, slug, summary, sections[], faqs[]}
- package: {name, priceFrom, features[], ctaLabel}
- portfolioItem: {title, slug, industry, isStudent, problem, solution, tech[], images[], liveUrl, result, testimonialRef}
- testimonial: {author, role, company, quote, avatar}
- blogPost: {title, slug, excerpt, body, coverImage, tags[], publishedAt, seo}
- siteSettings: {navLinks[], socials[], contact, seoDefaults}

17 — Non-functional requirements
Performance

- Use Next/Image, prefetching, route-level caching; defer non-critical JS; compress with Brotli; font optimization
  Accessibility
- WCAG 2.1 AA; keyboard traps avoided; focus states; color contrast ≥4.5:1; skip links
  Security & privacy
- Store minimal PII; PH Data Privacy Act compliance; TLS-only; server-side validation; secret rotation
- Backups: daily DB backups; export CMS content weekly

18 — Analytics & event taxonomy (examples)

- quote_opened, quote_submitted, quote_failed
- nav_click, cta_click, service_view, portfolio_view
- blog_read_start, blog_read_complete

19 — Environments & secrets

- Local, Preview (PR), Production on Vercel; feature flags via environment variables
- Secrets: SANITY*\*, SUPABASE*_, RESEND*API_KEY, UPSTASH*_, GA_MEASUREMENT_ID, CAPTCHA keys

20 — Risks & mitigations

- Scope creep: enforce MoSCoW; lock scope per phase
- Content delays: use placeholders and iterate; parallelize content production during build
- 3rd-party limits: graceful degradation and provider fallbacks
