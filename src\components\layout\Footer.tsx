import Link from 'next/link';
import { NavLink } from '@/types';

interface FooterProps {
  logo?: string;
  navLinks?: NavLink[];
  socialLinks?: {
    platform: string;
    href: string;
    icon: React.ReactNode;
  }[];
  contactInfo?: {
    address?: string;
    phone?: string;
    email?: string;
  };
}

export function Footer({
  logo = 'ReedSoft',
  navLinks = [],
  socialLinks = [],
  contactInfo,
}: FooterProps) {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-brand-neutral-dark text-white">
      <div className="container mx-auto px-4 py-12 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <Link href="/" className="mb-4 block text-2xl font-bold text-white">
              {logo}
            </Link>
            <p className="mb-6 max-w-md text-gray-300">
              Professional web development, mobile apps, and specialized student
              services in the Philippines. Fast, reliable, and affordable
              solutions.
            </p>

            {/* Social Links */}
            {socialLinks.length > 0 && (
              <div className="flex space-x-4">
                {socialLinks.map((social, index) => (
                  <Link
                    key={index}
                    href={social.href}
                    className="hover:text-brand-accent text-gray-300 transition-colors duration-200"
                    target="_blank"
                    rel="noopener noreferrer"
                    aria-label={social.platform}
                  >
                    {social.icon}
                  </Link>
                ))}
              </div>
            )}
          </div>

          {/* Quick Links */}
          {navLinks.length > 0 && (
            <div>
              <h3 className="mb-4 text-lg font-semibold">Quick Links</h3>
              <ul className="space-y-2">
                {navLinks.map(link => (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      className="hover:text-brand-accent text-gray-300 transition-colors duration-200"
                      {...(link.external && {
                        target: '_blank',
                        rel: 'noopener noreferrer',
                      })}
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Contact Info */}
          {contactInfo && (
            <div>
              <h3 className="mb-4 text-lg font-semibold">Contact</h3>
              <div className="space-y-2 text-gray-300">
                {contactInfo.address && (
                  <p className="flex items-start">
                    <svg
                      className="mt-0.5 mr-2 h-5 w-5 flex-shrink-0"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {contactInfo.address}
                  </p>
                )}
                {contactInfo.phone && (
                  <p className="flex items-center">
                    <svg
                      className="mr-2 h-5 w-5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                    </svg>
                    <Link
                      href={`tel:${contactInfo.phone}`}
                      className="hover:text-brand-accent transition-colors duration-200"
                    >
                      {contactInfo.phone}
                    </Link>
                  </p>
                )}
                {contactInfo.email && (
                  <p className="flex items-center">
                    <svg
                      className="mr-2 h-5 w-5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                    <Link
                      href={`mailto:${contactInfo.email}`}
                      className="hover:text-brand-accent transition-colors duration-200"
                    >
                      {contactInfo.email}
                    </Link>
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Bottom Bar */}
        <div className="mt-8 flex flex-col items-center justify-between border-t border-gray-700 pt-8 md:flex-row">
          <p className="text-sm text-gray-300">
            © {currentYear} {logo}. All rights reserved.
          </p>
          <div className="mt-4 flex space-x-6 md:mt-0">
            <Link
              href="/privacy"
              className="hover:text-brand-accent text-sm text-gray-300 transition-colors duration-200"
            >
              Privacy Policy
            </Link>
            <Link
              href="/terms"
              className="hover:text-brand-accent text-sm text-gray-300 transition-colors duration-200"
            >
              Terms of Service
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
