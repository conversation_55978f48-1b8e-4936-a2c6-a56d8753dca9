import Link from 'next/link';
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON><PERSON>,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface ServiceCardProps {
  title: string;
  summary: string;
  priceFrom?: number;
  link: string;
  features?: string[];
}

export function ServiceCard({
  title,
  summary,
  priceFrom,
  link,
  features,
}: ServiceCardProps) {
  return (
    <Card className="flex h-full flex-col border-gray-200 transition-shadow duration-300 hover:shadow-lg">
      <CardHeader>
        <CardTitle className="text-brand-neutral-dark text-xl font-semibold">
          {title}
        </CardTitle>
        {priceFrom && (
          <div className="text-brand-primary text-2xl font-bold">
            From ₱{priceFrom.toLocaleString()}
          </div>
        )}
      </CardHeader>

      <CardContent className="flex-grow">
        <p className="mb-4 leading-relaxed text-gray-600">{summary}</p>

        {features && features.length > 0 && (
          <ul className="space-y-2">
            {features.slice(0, 3).map((feature, index) => (
              <li key={index} className="flex items-start">
                <svg
                  className="text-brand-primary mt-0.5 mr-2 h-5 w-5 flex-shrink-0"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-sm text-gray-600">{feature}</span>
              </li>
            ))}
          </ul>
        )}
      </CardContent>

      <CardFooter>
        <Button
          asChild
          className="bg-brand-primary hover:bg-brand-primary/90 w-full"
        >
          <Link href={link}>Learn More</Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
